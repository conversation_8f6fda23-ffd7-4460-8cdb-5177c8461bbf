"""
Authentication utilities for Clerk.com integration
"""
import os
import jwt
import requests
from typing import Op<PERSON>, Dict, Any
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from functools import lru_cache
import logging

logger = logging.getLogger(__name__)

# Security scheme for extracting Bearer tokens
security = HTTPBearer()

class ClerkAuth:
    """Clerk authentication handler"""

    def __init__(self):
        self.clerk_secret_key = os.getenv("CLERK_SECRET_KEY")
        self.clerk_publishable_key = os.getenv("VITE_CLERK_PUBLISHABLE_KEY", "").replace("pk_test_", "").replace("pk_live_", "")
        self.jwks_cache = {}

        if not self.clerk_secret_key:
            logger.warning("CLERK_SECRET_KEY not found in environment variables")

    @lru_cache(maxsize=1)
    def get_jwks_url(self) -> str:
        """Get the JWKS URL for the Clerk instance"""
        if self.clerk_publishable_key:
            # Extract the instance from the publishable key
            return f"https://{self.clerk_publishable_key}.clerk.accounts.dev/.well-known/jwks.json"
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Clerk configuration not found"
            )

    def get_jwks(self) -> Dict[str, Any]:
        """Fetch JWKS from Clerk"""
        try:
            jwks_url = self.get_jwks_url()
            response = requests.get(jwks_url, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to fetch JWKS: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to fetch authentication keys"
            )

    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode a Clerk JWT token"""
        try:
            # Get the token header to find the key ID
            unverified_header = jwt.get_unverified_header(token)
            kid = unverified_header.get("kid")

            if not kid:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing key ID"
                )

            # Get JWKS and find the matching key
            jwks = self.get_jwks()
            key = None

            for jwk in jwks.get("keys", []):
                if jwk.get("kid") == kid:
                    key = jwt.algorithms.RSAAlgorithm.from_jwk(jwk)
                    break

            if not key:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: key not found"
                )

            # Verify and decode the token
            payload = jwt.decode(
                token,
                key,
                algorithms=["RS256"],
                options={"verify_aud": False}  # Clerk tokens don't always have audience
            )

            return payload

        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )

# Global auth instance
clerk_auth = ClerkAuth()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """
    Dependency to get the current authenticated user from JWT token
    """
    token = credentials.credentials
    user_data = clerk_auth.verify_token(token)

    # Extract user ID and other relevant information
    user_id = user_data.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token: missing user ID"
        )

    return {
        "user_id": user_id,
        "email": user_data.get("email"),
        "email_verified": user_data.get("email_verified", False),
        "first_name": user_data.get("given_name"),
        "last_name": user_data.get("family_name"),
        "full_name": user_data.get("name"),
        "raw_token_data": user_data
    }

async def get_optional_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))) -> Optional[Dict[str, Any]]:
    """
    Optional dependency to get the current user (returns None if not authenticated)
    """
    if not credentials:
        return None

    try:
        token = credentials.credentials
        user_data = clerk_auth.verify_token(token)

        # Extract user ID and other relevant information
        user_id = user_data.get("sub")
        if not user_id:
            return None

        return {
            "user_id": user_id,
            "email": user_data.get("email"),
            "email_verified": user_data.get("email_verified", False),
            "first_name": user_data.get("given_name"),
            "last_name": user_data.get("family_name"),
            "full_name": user_data.get("name"),
            "raw_token_data": user_data
        }
    except Exception:
        return None
