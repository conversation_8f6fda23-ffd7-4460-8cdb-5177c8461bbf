from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from typing import Dict, Any, Optional
from auth import get_current_user, get_optional_user

# Load environment variables
load_dotenv()

app = FastAPI(
    title="Web Interior Designer API",
    description="API for the Web Interior Designer application with Clerk authentication",
    version="0.1.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Public endpoints (no authentication required)
@app.get("/")
async def root():
    return {"message": "Hello World from Web Interior Designer API!"}

@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "service": "web-interior-designer-api"}

@app.get("/api/version")
async def version():
    return {"version": "0.1.0", "name": "Web Interior Designer API"}

# Protected endpoints (authentication required)
@app.get("/api/user/profile")
async def get_user_profile(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get the current user's profile information"""
    return {
        "user_id": current_user["user_id"],
        "email": current_user["email"],
        "email_verified": current_user["email_verified"],
        "first_name": current_user["first_name"],
        "last_name": current_user["last_name"],
        "full_name": current_user["full_name"]
    }

@app.get("/api/user/designs")
async def get_user_designs(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get designs for the current user"""
    # TODO: Implement actual design storage and retrieval
    return {
        "user_id": current_user["user_id"],
        "designs": [],
        "message": "Design storage not yet implemented"
    }

@app.post("/api/user/designs")
async def create_design(
    design_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Create a new design for the current user"""
    # TODO: Implement actual design creation
    return {
        "user_id": current_user["user_id"],
        "design_id": "placeholder-id",
        "message": "Design creation not yet implemented",
        "received_data": design_data
    }

# Optional authentication endpoints (work with or without auth)
@app.get("/api/public/designs")
async def get_public_designs(current_user: Optional[Dict[str, Any]] = Depends(get_optional_user)):
    """Get public designs (optionally filtered by user if authenticated)"""
    if current_user:
        return {
            "message": "Public designs for authenticated user",
            "user_id": current_user["user_id"],
            "designs": []
        }
    else:
        return {
            "message": "Public designs for anonymous user",
            "designs": []
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
