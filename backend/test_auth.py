"""
Tests for authentication functionality
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock
import jwt
from datetime import datetime, timedelta

from main import app

client = TestClient(app)


def test_health_check():
    """Test public health check endpoint"""
    response = client.get("/api/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy", "service": "web-interior-designer-api"}


def test_version():
    """Test public version endpoint"""
    response = client.get("/api/version")
    assert response.status_code == 200
    assert response.json() == {"version": "0.1.0", "name": "Web Interior Designer API"}


def test_public_designs_without_auth():
    """Test public designs endpoint without authentication"""
    response = client.get("/api/public/designs")
    assert response.status_code == 200
    data = response.json()
    assert "designs" in data
    assert "user_id" not in data
    assert "anonymous" in data["message"]


def test_protected_endpoint_without_auth():
    """Test protected endpoint without authentication"""
    response = client.get("/api/user/profile")
    assert response.status_code == 403  # Forbidden due to missing auth


def test_protected_endpoint_with_invalid_auth():
    """Test protected endpoint with invalid authentication"""
    headers = {"Authorization": "Bearer invalid_token"}
    response = client.get("/api/user/profile", headers=headers)
    assert response.status_code == 401  # Unauthorized due to invalid token


@patch('auth.clerk_auth.verify_token')
def test_user_profile_with_valid_auth(mock_verify_token):
    """Test user profile endpoint with valid authentication"""
    # Mock a valid token verification
    mock_verify_token.return_value = {
        "sub": "user_123",
        "email": "<EMAIL>",
        "email_verified": True,
        "given_name": "Test",
        "family_name": "User",
        "name": "Test User"
    }

    headers = {"Authorization": "Bearer valid_token"}
    response = client.get("/api/user/profile", headers=headers)
    assert response.status_code == 200

    data = response.json()
    assert data["user_id"] == "user_123"
    assert data["email"] == "<EMAIL>"
    assert data["email_verified"] == True
    assert data["first_name"] == "Test"
    assert data["last_name"] == "User"
    assert data["full_name"] == "Test User"


@patch('auth.clerk_auth.verify_token')
def test_user_designs_endpoint(mock_verify_token):
    """Test user designs endpoint with valid authentication"""
    # Mock a valid token verification
    mock_verify_token.return_value = {
        "sub": "user_123",
        "email": "<EMAIL>",
        "email_verified": True
    }

    headers = {"Authorization": "Bearer valid_token"}
    response = client.get("/api/user/designs", headers=headers)
    assert response.status_code == 200

    data = response.json()
    assert data["user_id"] == "user_123"
    assert "designs" in data


@patch('auth.clerk_auth.verify_token')
def test_create_design_endpoint(mock_verify_token):
    """Test create design endpoint with valid authentication"""
    # Mock a valid token verification
    mock_verify_token.return_value = {
        "sub": "user_123",
        "email": "<EMAIL>",
        "email_verified": True
    }

    design_data = {"name": "Test Design", "rooms": []}
    headers = {"Authorization": "Bearer valid_token"}
    response = client.post("/api/user/designs", json=design_data, headers=headers)
    assert response.status_code == 200

    data = response.json()
    assert data["user_id"] == "user_123"
    assert "design_id" in data


@patch('auth.clerk_auth.verify_token')
def test_public_designs_with_auth(mock_verify_token):
    """Test public designs endpoint with authentication"""
    # Mock a valid token verification
    mock_verify_token.return_value = {
        "sub": "user_123",
        "email": "<EMAIL>",
        "email_verified": True
    }

    headers = {"Authorization": "Bearer valid_token"}
    response = client.get("/api/public/designs", headers=headers)
    assert response.status_code == 200

    data = response.json()
    assert data["user_id"] == "user_123"
    assert "authenticated" in data["message"]


def test_auth_error_handling():
    """Test various authentication error scenarios"""
    # Test with malformed Authorization header
    headers = {"Authorization": "InvalidFormat"}
    response = client.get("/api/user/profile", headers=headers)
    assert response.status_code == 403  # FastAPI HTTPBearer returns 403 for malformed headers

    # Test with empty token
    headers = {"Authorization": "Bearer "}
    response = client.get("/api/user/profile", headers=headers)
    assert response.status_code == 403  # FastAPI HTTPBearer returns 403 for empty tokens


@patch('auth.clerk_auth.verify_token')
def test_token_verification_error_handling(mock_verify_token):
    """Test token verification error handling"""
    # Mock token verification to raise an HTTPException
    from fastapi import HTTPException, status
    mock_verify_token.side_effect = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Authentication failed"
    )

    headers = {"Authorization": "Bearer some_token"}
    response = client.get("/api/user/profile", headers=headers)
    assert response.status_code == 401


if __name__ == "__main__":
    pytest.main([__file__])
