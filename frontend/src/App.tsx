import { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, CloseButton, Dialog, FileUpload, Heading, HStack, Icon, Image, Link, Portal, Separator, SimpleGrid, Steps, Text, Textarea, VStack } from '@chakra-ui/react';
import { LuUpload, LuRefreshCw, LuCamera, LuImage, LuGrid3X3, LuInfo, LuZoomIn, LuShare, LuSettings, LuPlus } from 'react-icons/lu';
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/clerk-react';
import { QuickTipsDrawer } from './QuickTipsDrawer';
import ApiTest from './components/ApiTest';
import './styles.css'; // Assuming a styles.css file exists for basic styling

interface Style {
  name: string;
  description: string;
  image: string;
}

function App() {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedStyle, setSelectedStyle] = useState<Style | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imageDataUrl, setImageDataUrl] = useState<string | null>(null);
  const [redesignedImageUrl, setRedesignedImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customizations, setCustomizations] = useState('');
  const [isQuickTipsOpen, setIsQuickTipsOpen] = useState(false);
  const [isZoomModalOpen, setIsZoomModalOpen] = useState(false);
  const [zoomedImageUrl, setZoomedImageUrl] = useState<string | null>(null);
  const [zoomedImageAlt, setZoomedImageAlt] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [currentStep]);

  const styles: Array<Style> = [
    { name: 'Minimalistic', description: 'Less is more - clean spaces, neutral tones, and clutter-free serenity.', image: '/images/interior_design_styles/minimalistic_1.jpg' },
    { name: 'Farmhouse', description: 'Cozy country charm with weathered wood, vintage finds, and homey comfort.', image: '/images/interior_design_styles/farmhouse_1.jpg' },
    { name: 'Modern', description: 'Clean lines, sleek surfaces, and bold accents in sophisticated simplicity.', image: '/images/interior_design_styles/modern_1.jpg' },
    { name: 'Mid-century', description: 'Retro aesthetic with clean lines and organic forms.', image: '/images/interior_design_styles/mid_century_1.jpg' },
    { name: 'Scandinavian', description: 'Simple, functional, and light with natural elements.', image: '/images/interior_design_styles/scandinavian_1.jpg' },
    {
      name: 'Modern Colonial',
      description: 'Heritage meets elegance with arched forms, earthy textures, and warm minimalism.',
      image: '/images/interior_design_styles/modern_colonial_1.png'
    },
    { name: 'Industrial', description: 'Raw textures, exposed elements, and utilitarian appeal.', image: '/images/interior_design_styles/industrial_1.jpg' },
    { name: 'Japanese', description: 'Zen-inspired harmony with natural materials and mindful simplicity.', image: '/images/interior_design_styles/japanese_1.jpg' },
    { name: 'Mediterranean', description: 'Sun-soaked warmth with terracotta, blues, and coastal elegance.', image: '/images/interior_design_styles/mediterrean_1.jpg' },
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImageDataUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleStyleSelect = (style: Style) => {
    setSelectedStyle(style);
    setCurrentStep(3); // Navigate directly to review step
  };

  const handleZoomImage = (imageUrl: string, altText: string) => {
    setZoomedImageUrl(imageUrl);
    setZoomedImageAlt(altText);
    setIsZoomModalOpen(true);
  };

  const handleCloseZoom = () => {
    setIsZoomModalOpen(false);
    setZoomedImageUrl(null);
    setZoomedImageAlt('');
  };

  // Mock response function for dry mode
  const getMockResponse = async (): Promise<{ redesignedImageUrl: string }> => {
    // Simulate API delay for realistic testing
    await new Promise(resolve => setTimeout(resolve, 500));

    // Return the input image as the "transformed" result
    // This provides a more realistic testing experience using the actual uploaded image
    if (!imageDataUrl) {
      throw new Error('No input image available for mock response');
    }
    return { redesignedImageUrl: imageDataUrl };
  };

  const handleGenerateDesign = async () => {
    if (!selectedFile || !selectedStyle || !imageDataUrl) {
      setError("Please select an image and a style.");
      return;
    }

    setIsLoading(true);
    setError(null);
    setRedesignedImageUrl(null); // Clear previous result

    try {
      // Check if dry mode is enabled
      const isDryMode = import.meta.env.VITE_DRY_MODE;

      let result;
      if (isDryMode) {
        console.log('🔧 DRY_MODE enabled - using mock response instead of API call');
        result = await getMockResponse();
      } else {
        const response = await fetch('http://127.0.0.1:5000/process-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            imageDataUrl: imageDataUrl,
            style: selectedStyle.name,
            customizations: customizations.trim() || undefined,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }

        result = await response.json();
      }

      setRedesignedImageUrl(result.redesignedImageUrl);
      setCurrentStep(4); // Move to results step on success

    } catch (e: any) {
      console.error("Error generating design:", e);
      setError(`Failed to process image: ${e.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const steps = [
    { title: '' },
    { title: '' },
    { title: '' },
    { title: '' }
  ];

  return (
    <>
      {/* Authentication Header */}
      <Box bg="white" borderBottom="1px" borderColor="gray.200" px={6} py={4}>
        <HStack justify="space-between">
          <Heading size="lg" color="blue.600">
            Interior Designer
          </Heading>
          <HStack spacing={4}>
            <SignedOut>
              <SignInButton>
                <Button colorScheme="blue" variant="outline">
                  Sign In
                </Button>
              </SignInButton>
            </SignedOut>
            <SignedIn>
              <UserButton />
            </SignedIn>
          </HStack>
        </HStack>
      </Box>

      {/* Main App Content - Only show when signed in */}
      <SignedOut>
        <Box
          height="calc(100vh - 80px)"
          display="flex"
          alignItems="center"
          justifyContent="center"
          bg="gray.50"
        >
          <VStack spacing={6} textAlign="center">
            <Heading size="xl" color="gray.700">
              Welcome to Interior Designer
            </Heading>
            <Text fontSize="lg" color="gray.600" maxW="md">
              Transform your space with AI-powered interior design. Sign in to get started with Google OAuth, Apple Sign-In, or passwordless email.
            </Text>
            <SignInButton>
              <Button colorScheme="blue" size="lg">
                Get Started
              </Button>
            </SignInButton>
          </VStack>
        </Box>
      </SignedOut>

      <SignedIn>
        {/* API Integration Test - Remove this in production */}
        <Box p={6} bg="blue.50" borderBottom="1px" borderColor="blue.200">
          <ApiTest />
        </Box>

        {/* Hidden file input for Change Photo functionality */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileSelect}
          accept="image/*"
          style={{ display: 'none' }}
        />

        {/* Upload Section */}
        {currentStep === 1 && (
          <>
            {/* White Background Section - Header */}
            <Box bg="white" py={6} px={2}>
              <Box maxW="container.xl" mx="auto" px={4}>
                {/* Step Indicator */}
                <div className="mb-6">
                  <Steps.Root step={currentStep - 1} count={steps.length} colorPalette="blue">
                    <Steps.List>
                      {steps.map((step, index) => (
                        <Steps.Item key={index} index={index}>
                          <Steps.Indicator />
                          <Steps.Title>{step.title}</Steps.Title>
                          <Steps.Separator />
                        </Steps.Item>
                      ))}
                    </Steps.List>
                  </Steps.Root>
                </div>

                {/* Header Content */}
                <VStack gap={4} textAlign="center">
                  <Box>
                    <Heading as="h3" fontSize="2xl" fontWeight="bold" mt={5} mb={4}>Upload Your Space Photo</Heading>
                    <Text color="gray.600" mb={1}>Submit a clear photo of the space to redesign</Text>
                  </Box>
                </VStack>
              </Box>
            </Box>

            {/* Gray Background Section - Upload Area and Full Height */}
            <Box bg="gray.50" py={8} px={2} minH="100vh">
              <Box maxW="container.xl" mx="auto" px={4}>
                {/* Photo Tips Button - Top Right */}
                <Box position="relative" mb={4}>
                  <Button
                    position="absolute"
                    top={4}
                    right={4}
                    colorPalette="blue"
                    size="sm"
                    borderRadius="full"
                    px={4}
                    py={2}
                    fontSize="sm"
                    fontWeight="medium"
                    onClick={() => setIsQuickTipsOpen(true)}
                  >
                    <HStack gap={1}>
                      <Text>Quick Tips</Text>
                      <Icon boxSize={4}>
                        <LuInfo />
                      </Icon>
                    </HStack>
                  </Button>
                </Box>
                <VStack gap={4} textAlign="center">
                  {!imageDataUrl ? (
                    <Box
                      w="full"
                      animationName="fadeIn"
                      animationDuration="0.3s"
                      animationTimingFunction="ease-out"
                    >
                      <FileUpload.Root
                        accept="image/*"
                        maxFiles={1}
                        onFileChange={(details) => {
                          if (details.acceptedFiles.length > 0) {
                            const file = details.acceptedFiles[0];
                            setSelectedFile(file);

                            const reader = new FileReader();
                            reader.onloadend = () => {
                              setImageDataUrl(reader.result as string);
                            };
                            reader.readAsDataURL(file);
                          } else {
                            setSelectedFile(null);
                            setImageDataUrl(null);
                          }
                        }}
                        w="full"
                        alignItems="stretch"
                      >
                        <FileUpload.HiddenInput />
                        <FileUpload.Dropzone>
                          <VStack gap={4} py={8}>
                            <Icon size="lg" color="blue.500">
                              <LuUpload />
                            </Icon>
                            <VStack gap={2}>
                              <Text fontWeight="medium">Choose Photo</Text>
                              <Text color="gray.500" fontSize="sm">or drag and drop here</Text>
                            </VStack>
                          </VStack>
                        </FileUpload.Dropzone>
                      </FileUpload.Root>
                    </Box>
                  ) : (
                    <Box
                      w="full"
                      h="256px"
                      rounded="md"
                      overflow="hidden"
                      animationName="fadeIn"
                      animationDuration="0.6s"
                      animationDelay="0.2s"
                      animationTimingFunction="ease-out"
                      animationFillMode="both"
                    >
                      <Image
                        src={imageDataUrl}
                        alt="Selected space photo"
                        w="full"
                        h="full"
                        objectFit="cover"
                        rounded="md"
                      />
                    </Box>
                  )}

                  {!imageDataUrl ? (
                    <Button
                      colorScheme={selectedFile ? "blue" : "gray"}
                      size="lg"
                      px={8}
                      onClick={() => selectedFile && setCurrentStep(2)}
                      disabled={!selectedFile}
                      animationName="fadeIn"
                      animationDuration="0.3s"
                      animationTimingFunction="ease-out"
                    >
                      Continue &rarr;
                    </Button>
                  ) : (
                    <HStack
                      gap={4}
                      w="full"
                      animationName="slideInUp"
                      animationDuration="0.6s"
                      animationDelay="0.3s"
                      animationTimingFunction="ease-out"
                      animationFillMode="both"
                    >
                      <Button
                        variant="outline"
                        colorScheme="gray"
                        size="lg"
                        flex={1}
                        onClick={triggerFileSelect}
                      >
                        <HStack gap={2}>
                          <Icon boxSize={4}>
                            <LuRefreshCw />
                          </Icon>
                          <Text>Change Photo</Text>
                        </HStack>
                      </Button>
                      <Button
                        colorScheme="blue"
                        size="lg"
                        flex={1}
                        onClick={() => setCurrentStep(2)}
                      >
                        Continue &rarr;
                      </Button>
                    </HStack>
                  )}
                </VStack>
              </Box>
            </Box>
          </>
        )}

        {/* Select Style Section */}
        {currentStep === 2 && (
          <>
            {/* White Background Section - Header */}
            <Box bg="white" py={6} px={2}>
              <Box maxW="container.xl" mx="auto" px={4}>
                {/* Step Indicator */}
                <div className="mb-6">
                  <Steps.Root step={currentStep - 1} count={steps.length} colorPalette="blue">
                    <Steps.List>
                      {steps.map((step, index) => (
                        <Steps.Item key={index} index={index}>
                          <Steps.Indicator />
                          <Steps.Title>{step.title}</Steps.Title>
                          <Steps.Separator />
                        </Steps.Item>
                      ))}
                    </Steps.List>
                  </Steps.Root>
                </div>

                {/* Header Content */}
                <VStack gap={4} textAlign="center">
                  <Box>
                    <Heading as="h3" fontSize="2xl" fontWeight="bold" mt={5} mb={4}>Select a Design Style</Heading>
                    <Text color="gray.600" mb={1}>Pick a style to guide your space redesign.</Text>
                  </Box>
                </VStack>
              </Box>
            </Box>

            {/* Gray Background Section - Content */}
            <Box bg="gray.50" py={8} px={2} minH="100vh">
              <Box maxW="container.xl" mx="auto" px={4}>
                <VStack gap={6} mb={6}>

                  <SimpleGrid columns={{ base: 1, md: 2 }} gap={4} w="full">
                    {styles.map((style, index) => (
                      <Card.Root
                        key={index}
                        cursor="pointer"
                        _hover={{ borderColor: "blue.500" }}
                        onClick={() => handleStyleSelect(style)}
                        variant={selectedStyle?.name === style.name ? "elevated" : "outline"}
                        borderColor={selectedStyle?.name === style.name ? "blue.500" : "gray.300"}
                      >
                        <Image
                          src={style.image}
                          alt={style.name}
                          roundedTop="md"
                          w="full"
                          h="200px"
                          objectFit="cover"
                        />
                        <Card.Body gap={2}>
                          <Card.Title fontSize="lg" fontWeight="bold">{style.name}</Card.Title>
                          <Card.Description color="gray.600">{style.description}</Card.Description>
                        </Card.Body>
                      </Card.Root>
                    ))}
                  </SimpleGrid>

                  <HStack justify="flex-start" w="full" mt={6}>
                    <Button
                      variant="outline"
                      colorScheme="gray"
                      px={6}
                      py={2}
                      onClick={() => setCurrentStep(1)}
                    >
                      &larr; Back
                    </Button>
                  </HStack>

                  {error && (
                    <Text color="red.500" textAlign="center" mt={4}>{error}</Text>
                  )}
                </VStack>
              </Box>
            </Box>
          </>
        )}



        {/* Review Design Section - Step 3 */}
        {currentStep === 3 && selectedStyle && (
          <>
            {/* White Background Section - Header */}
            <Box bg="white" py={6} px={2}>
              <Box maxW="container.xl" mx="auto" px={4}>
                {/* Step Indicator */}
                <div className="mb-6">
                  <Steps.Root step={currentStep - 1} count={steps.length} colorPalette="blue">
                    <Steps.List>
                      {steps.map((step, index) => (
                        <Steps.Item key={index} index={index}>
                          <Steps.Indicator />
                          <Steps.Title>{step.title}</Steps.Title>
                          <Steps.Separator />
                        </Steps.Item>
                      ))}
                    </Steps.List>
                  </Steps.Root>
                </div>

                {/* Header Content */}
                <VStack gap={4} textAlign="center">
                  <Box>
                    <Heading as="h3" fontSize="2xl" fontWeight="bold" mt={5} mb={4}>Summary</Heading>
                  </Box>
                </VStack>
              </Box>
            </Box>

            {/* Gray Background Section - Content */}
            <Box bg="gray.50" py={8} px={2} minH="100vh">
              <Box maxW="container.xl" mx="auto" px={4}>
                <VStack gap={6} mb={6} maxW="container.md" mx="auto">

                  {/* Source Section */}
                  <Box w="full">
                    <HStack gap={3} align="center" mb={4}>
                      <Icon size="md" color="blue.500">
                        <LuImage />
                      </Icon>
                      <Heading as="h4" fontSize="xl" fontWeight="bold" color="gray.800">
                        Source
                      </Heading>
                    </HStack>
                    <Box position="relative" w="full" rounded="md" overflow="hidden">
                      {imageDataUrl ? (
                        <>
                          <Image
                            src={imageDataUrl}
                            alt="Uploaded space photo"
                            w="full"
                            h="300px"
                            objectFit="cover"
                          />
                          <Button
                            position="absolute"
                            bottom={4}
                            right={4}
                            size="sm"
                            colorScheme="blue"
                            bg="white"
                            color="blue.500"
                            _hover={{ bg: "gray.50" }}
                            borderRadius="full"
                            px={3}
                            py={2}
                            fontSize="sm"
                            fontWeight="medium"
                            onClick={triggerFileSelect}
                            boxShadow="md"
                          >
                            <HStack gap={1}>
                              <Icon boxSize={4}>
                                <LuRefreshCw />
                              </Icon>
                              <Text>Change Upload</Text>
                            </HStack>
                          </Button>
                        </>
                      ) : (
                        <Box
                          w="full"
                          h="300px"
                          bg="gray.200"
                          rounded="md"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <VStack gap={2}>
                            <Icon size="xl" color="gray.400">
                              <LuCamera />
                            </Icon>
                            <Text color="gray.500" fontSize="sm">No image selected</Text>
                          </VStack>
                        </Box>
                      )}
                    </Box>
                  </Box>

                  {/* Style Reference Section */}
                  <Box w="full">
                    <HStack gap={3} align="center" mb={4}>
                      <Icon size="md" color="blue.500">
                        <LuCamera />
                      </Icon>
                      <Heading as="h4" fontSize="xl" fontWeight="bold" color="gray.800">
                        Style Reference
                      </Heading>
                    </HStack>
                    <Card.Root variant="outline" bg="gray.50" w="full">
                      <Card.Body p={6}>
                        <VStack gap={3} align="flex-start" w="full">
                          <Text fontWeight="bold" fontSize="lg" color="gray.800">
                            {selectedStyle.name}
                          </Text>
                          <HStack gap={4} align="flex-start" w="full">
                            <Image
                              src={selectedStyle.image}
                              alt={selectedStyle.name}
                              rounded="md"
                              w="80px"
                              h="80px"
                              objectFit="cover"
                              flexShrink={0}
                            />
                            <VStack gap={2} align="flex-start" flex={1}>
                              <Text fontSize="sm" color="gray.600" lineHeight="relaxed">
                                {selectedStyle.description}
                              </Text>
                              <Button
                                size="sm"
                                color="blue.500"
                                fontSize="sm"
                                fontWeight="medium"
                                variant="ghost"
                                _hover={{ color: "blue.600" }}
                                onClick={() => setCurrentStep(2)}
                                p={0}
                                h="auto"
                              >
                                Change
                              </Button>
                            </VStack>
                          </HStack>
                        </VStack>
                      </Card.Body>
                    </Card.Root>
                  </Box>

                  {/* Additional Tweaks Section */}
                  <Box w="full">
                    <HStack gap={3} align="center" mb={4}>
                      <Icon size="md" color="blue.500">
                        <LuGrid3X3 />
                      </Icon>
                      <Heading as="h4" fontSize="xl" fontWeight="bold" color="gray.800">
                        Additional Tweaks
                      </Heading>
                      <Text fontSize="sm" color="gray.500" ml="auto">
                        Optional
                      </Text>
                    </HStack>
                    <Textarea
                      placeholder="Describe what you want to add, remove or replace..."
                      value={customizations}
                      onChange={(e) => setCustomizations(e.target.value)}
                      rows={4}
                      resize="vertical"
                      w="full"
                      bg="white"
                      border="1px solid"
                      borderColor="gray.300"
                      rounded="md"
                      _focus={{
                        borderColor: "blue.500",
                        boxShadow: "0 0 0 1px var(--chakra-colors-blue-500)"
                      }}
                    />
                  </Box>

                  {/* Generate Design Button */}
                  <Button
                    colorScheme="blue"
                    size="lg"
                    w="full"
                    onClick={handleGenerateDesign}
                    disabled={isLoading}
                    mt={6}
                    bg="gray.800"
                    color="white"
                    _hover={{ bg: "gray.700" }}
                    _active={{ bg: "gray.900" }}
                    py={6}
                    fontSize="md"
                    fontWeight="semibold"
                  >
                    {isLoading ? 'Generating Design...' : 'Generate Design →'}
                  </Button>

                  {error && (
                    <Text color="red.500" textAlign="center" mt={4}>{error}</Text>
                  )}

                  {/* Navigation */}
                  <HStack justify="flex-start" w="full" mt={6}>
                    <Button
                      variant="outline"
                      colorScheme="gray"
                      px={6}
                      py={2}
                      onClick={() => setCurrentStep(2)}
                    >
                      ← Back
                    </Button>
                  </HStack>
                </VStack>
              </Box>
            </Box>
          </>
        )}

        {/* Results Section - Step 4 */}
        {currentStep === 4 && selectedStyle && redesignedImageUrl && (
          <>
            {/* White Background Section - Header */}
            <Box bg="white" py={6} px={2}>
              <Box maxW="container.xl" mx="auto" px={4}>
                {/* Step Indicator */}
                <div className="mb-6">
                  <Steps.Root step={currentStep - 1} count={steps.length} colorPalette="blue">
                    <Steps.List>
                      {steps.map((step, index) => (
                        <Steps.Item key={index} index={index}>
                          <Steps.Indicator />
                          <Steps.Title>{step.title}</Steps.Title>
                          <Steps.Separator />
                        </Steps.Item>
                      ))}
                    </Steps.List>
                  </Steps.Root>
                </div>

                {/* Header Content */}
                <VStack gap={4} textAlign="center">
                  <Box>
                    <Heading as="h3" fontSize="2xl" fontWeight="bold" mt={5} mb={4}>Your Redesigned Space</Heading>
                    <Text color="gray.600" mb={1}>Here's your space in {selectedStyle.name} style</Text>
                  </Box>
                </VStack>
              </Box>
            </Box>

            {/* Gray Background Section - Content */}
            <Box bg="gray.50" py={8} px={2} minH="100vh">
              <Box maxW="container.xl" mx="auto" px={4}>
                <VStack gap={6} mb={6}>

                  <Box display="flex" justifyContent="center" mb={3}>
                    {/* Display Before/After Images */}
                    <Box position="relative" w="full" maxW="lg">
                      {imageDataUrl && (
                        <Box
                          position="relative"
                          w="50%"
                          display="inline-block"
                          cursor="pointer"
                          onClick={() => handleZoomImage(imageDataUrl, "Original space photo")}
                          _hover={{ transform: "scale(1.02)" }}
                          transition="transform 0.2s ease"
                        >
                          <Image
                            src={imageDataUrl}
                            alt="Before"
                            w="full"
                            roundedLeft="lg"
                          />
                          <Button
                            position="absolute"
                            top={2}
                            right={2}
                            size="sm"
                            bg="rgba(0, 0, 0, 0.2)"
                            color="whiteAlpha.900"
                            borderRadius="full"
                            p={1.5}
                            minW="auto"
                            h="auto"
                            pointerEvents="none"
                            _hover={{
                              color: "white",
                              borderColor: "blackAlpha.400",
                              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.4)"
                            }}
                          >
                            <Icon boxSize={4}>
                              <LuZoomIn />
                            </Icon>
                          </Button>
                        </Box>
                      )}
                      {redesignedImageUrl && (
                        <Box
                          position="relative"
                          w="50%"
                          display="inline-block"
                          cursor="pointer"
                          onClick={() => handleZoomImage(redesignedImageUrl, "Redesigned space")}
                          _hover={{ transform: "scale(1.02)" }}
                          transition="transform 0.2s ease"
                        >
                          <Image
                            src={redesignedImageUrl}
                            alt="After"
                            w="full"
                            roundedRight="lg"
                          />
                          <Button
                            position="absolute"
                            top={2}
                            right={2}
                            size="sm"
                            bg="transparent"
                            color="whiteAlpha.900"
                            borderRadius="full"
                            p={1.5}
                            minW="auto"
                            h="auto"
                            pointerEvents="none"
                            border="1px solid"
                            borderColor="blackAlpha.300"
                            boxShadow="0 1px 3px rgba(0, 0, 0, 0.3)"
                            _hover={{
                              color: "white",
                              borderColor: "blackAlpha.400",
                              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.4)"
                            }}
                          >
                            <Icon boxSize={4}>
                              <LuZoomIn />
                            </Icon>
                          </Button>
                          {/* Share Button */}
                          <Button
                            position="absolute"
                            bottom={2}
                            right={2}
                            size="sm"
                            bg="white"
                            color="black"
                            borderRadius="full"
                            p={1.5}
                            minW="auto"
                            h="auto"
                            pointerEvents="none"
                            boxShadow="0 2px 4px rgba(0, 0, 0, 0.15)"
                            _hover={{
                              bg: "gray.50",
                              boxShadow: "0 2px 6px rgba(0, 0, 0, 0.2)"
                            }}
                          >
                            <Icon boxSize={4}>
                              <LuShare />
                            </Icon>
                          </Button>
                        </Box>
                      )}
                    </Box>
                  </Box>

                  {/* Success Alert */}
                  <Alert.Root status="success" variant="subtle">
                    <Alert.Indicator />
                    <Alert.Title>
                      The redesign has been saved to your{' '}
                      <Link href="/" color="green.600" textDecoration="underline" _hover={{ color: "green.700" }}>
                        collection
                      </Link>
                    </Alert.Title>
                  </Alert.Root>

                  {/* Separator */}
                  <Separator w="full" />

                  {/* What would you like to do next? Section */}
                  <VStack gap={4} align="center" w="full">
                    <Text fontSize="lg" fontWeight="medium" color="gray.700">
                      What would you like to do next?
                    </Text>
                    <VStack gap={3} w="full">
                      <Card.Root
                        variant="outline"
                        cursor="pointer"
                        _hover={{ borderColor: "blue.500", bg: "blue.50" }}
                        onClick={() => setCurrentStep(3)}
                        w="full"
                        p={4}
                      >
                        <HStack gap={3} align="center">
                          <Icon boxSize={5} color="blue.500">
                            <LuRefreshCw />
                          </Icon>
                          <VStack gap={1} align="flex-start" flex={1}>
                            <Text fontWeight="bold" fontSize="md">Redesign From This Result</Text>
                            <Text fontSize="sm" color="gray.600">Use this design as your new starting point</Text>
                          </VStack>
                        </HStack>
                      </Card.Root>

                      <Card.Root
                        variant="outline"
                        cursor="pointer"
                        _hover={{ borderColor: "blue.500", bg: "blue.50" }}
                        onClick={() => setCurrentStep(3)}
                        w="full"
                        p={4}
                      >
                        <HStack gap={3} align="center">
                          <Icon boxSize={5} color="blue.500">
                            <LuSettings />
                          </Icon>
                          <VStack gap={1} align="flex-start" flex={1}>
                            <Text fontWeight="bold" fontSize="md">Refine Current Choices</Text>
                            <Text fontSize="sm" color="gray.600">Adjust with same room & preferences</Text>
                          </VStack>
                        </HStack>
                      </Card.Root>

                      <Card.Root
                        variant="outline"
                        cursor="pointer"
                        _hover={{ borderColor: "blue.500", bg: "blue.50" }}
                        onClick={() => setCurrentStep(1)}
                        w="full"
                        p={4}
                      >
                        <HStack gap={3} align="center">
                          <Icon boxSize={5} color="blue.500">
                            <LuPlus />
                          </Icon>
                          <VStack gap={1} align="flex-start" flex={1}>
                            <Text fontWeight="bold" fontSize="md">Start Fresh</Text>
                            <Text fontSize="sm" color="gray.600">Begin with a completely new room</Text>
                          </VStack>
                        </HStack>
                      </Card.Root>
                    </VStack>
                  </VStack>
                </VStack>
              </Box>
            </Box>
          </>
        )}

        {/* Zoom Modal */}
        <Dialog.Root
          open={isZoomModalOpen}
          onOpenChange={(e) => !e.open && handleCloseZoom()}
          size="full"
          placement="center"
          closeOnInteractOutside={true}
        >
          <Portal>
            <Dialog.Backdrop />
            <Dialog.Positioner>
              <Dialog.Content
                p={0}
                bg="transparent"
                boxShadow="none"
                h="100vh"
                w="100vw"
                display="flex"
                alignItems="center"
                justifyContent="center"
                onClick={handleCloseZoom}
              >
                {zoomedImageUrl && (
                  <Box
                    position="relative"
                    display="inline-block"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Image
                      src={zoomedImageUrl}
                      alt={zoomedImageAlt}
                      maxW="95vw"
                      maxH="95vh"
                      objectFit="contain"
                      rounded="lg"
                      boxShadow="2xl"
                    />
                    <Dialog.CloseTrigger
                      asChild
                      position="absolute"
                      top={3}
                      right={3}
                      zIndex={10}
                    >
                      <CloseButton
                        size="sm"
                        bg="blackAlpha.600"
                        color="white"
                        _hover={{ bg: "blackAlpha.700" }}
                        borderRadius="full"
                      />
                    </Dialog.CloseTrigger>
                  </Box>
                )}
              </Dialog.Content>
            </Dialog.Positioner>
          </Portal>
        </Dialog.Root>

        {/* Quick Tips Drawer */}
        <QuickTipsDrawer
          isOpen={isQuickTipsOpen}
          onClose={() => setIsQuickTipsOpen(false)}
        />
      </SignedIn>
    </>
  );
}

export default App;
