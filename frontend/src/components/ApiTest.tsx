import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  VStack, 
  HStack, 
  Text, 
  Heading, 
  Alert, 
  AlertIcon,
  Code,
  Spinner,
  useColorModeValue
} from '@chakra-ui/react';
import { useApiService } from '../services/api';

const ApiTest: React.FC = () => {
  const [results, setResults] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const api = useApiService();
  const bgColor = useColorModeValue('gray.50', 'gray.700');

  const testEndpoint = async (name: string, apiCall: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [name]: true }));
    setErrors(prev => ({ ...prev, [name]: '' }));
    
    try {
      const result = await apiCall();
      setResults(prev => ({ ...prev, [name]: result }));
    } catch (error) {
      setErrors(prev => ({ ...prev, [name]: error instanceof Error ? error.message : 'Unknown error' }));
    } finally {
      setLoading(prev => ({ ...prev, [name]: false }));
    }
  };

  const tests = [
    {
      name: 'Health Check',
      key: 'health',
      call: () => api.getHealth(),
      description: 'Test public health endpoint'
    },
    {
      name: 'Version',
      key: 'version',
      call: () => api.getVersion(),
      description: 'Test public version endpoint'
    },
    {
      name: 'Public Designs',
      key: 'publicDesigns',
      call: () => api.getPublicDesigns(),
      description: 'Test public designs endpoint (no auth)'
    },
    {
      name: 'User Profile',
      key: 'userProfile',
      call: () => api.getUserProfile(),
      description: 'Test protected user profile endpoint'
    },
    {
      name: 'User Designs',
      key: 'userDesigns',
      call: () => api.getUserDesigns(),
      description: 'Test protected user designs endpoint'
    },
    {
      name: 'Create Design',
      key: 'createDesign',
      call: () => api.createDesign({ name: 'Test Design', rooms: [] }),
      description: 'Test protected create design endpoint'
    }
  ];

  const runAllTests = async () => {
    for (const test of tests) {
      await testEndpoint(test.key, test.call);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  return (
    <Box p={6} bg={bgColor} borderRadius="md" maxW="4xl" mx="auto">
      <VStack spacing={6} align="stretch">
        <Heading size="lg" textAlign="center">
          API Integration Test
        </Heading>
        
        <Text textAlign="center" color="gray.600">
          Test the connection between frontend and backend with authentication
        </Text>

        <HStack justify="center">
          <Button colorScheme="blue" onClick={runAllTests}>
            Run All Tests
          </Button>
        </HStack>

        <VStack spacing={4} align="stretch">
          {tests.map((test) => (
            <Box key={test.key} p={4} border="1px" borderColor="gray.200" borderRadius="md">
              <HStack justify="space-between" mb={2}>
                <VStack align="start" spacing={1}>
                  <Text fontWeight="bold">{test.name}</Text>
                  <Text fontSize="sm" color="gray.600">{test.description}</Text>
                </VStack>
                <Button
                  size="sm"
                  colorScheme="blue"
                  variant="outline"
                  onClick={() => testEndpoint(test.key, test.call)}
                  isLoading={loading[test.key]}
                  loadingText="Testing..."
                >
                  Test
                </Button>
              </HStack>

              {loading[test.key] && (
                <HStack>
                  <Spinner size="sm" />
                  <Text>Testing endpoint...</Text>
                </HStack>
              )}

              {errors[test.key] && (
                <Alert status="error" mt={2}>
                  <AlertIcon />
                  <Text fontSize="sm">{errors[test.key]}</Text>
                </Alert>
              )}

              {results[test.key] && !errors[test.key] && (
                <Alert status="success" mt={2}>
                  <AlertIcon />
                  <VStack align="start" spacing={2} w="full">
                    <Text fontSize="sm" fontWeight="bold">Success!</Text>
                    <Code p={2} borderRadius="md" w="full" fontSize="xs">
                      {JSON.stringify(results[test.key], null, 2)}
                    </Code>
                  </VStack>
                </Alert>
              )}
            </Box>
          ))}
        </VStack>
      </VStack>
    </Box>
  );
};

export default ApiTest;
