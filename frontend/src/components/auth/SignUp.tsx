import React from 'react';
import { SignUp as ClerkSignUp } from '@clerk/clerk-react';
import { Box, Container, Heading, Text, VStack } from '@chakra-ui/react';

const SignUp: React.FC = () => {
  return (
    <Container maxW="md" py={12}>
      <VStack spacing={8}>
        <Box textAlign="center">
          <Heading size="lg" mb={2}>
            Create Your Account
          </Heading>
          <Text color="gray.600">
            Join Interior Designer and start creating beautiful spaces
          </Text>
        </Box>
        
        <Box w="full">
          <ClerkSignUp 
            appearance={{
              elements: {
                formButtonPrimary: {
                  backgroundColor: '#3182CE',
                  '&:hover': {
                    backgroundColor: '#2C5282'
                  }
                },
                card: {
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  borderRadius: '8px'
                }
              }
            }}
            redirectUrl="/"
            signInUrl="/sign-in"
          />
        </Box>
      </VStack>
    </Container>
  );
};

export default SignUp;
