import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { Box, Container, Heading, Text, VStack } from '@chakra-ui/react';

const SignIn: React.FC = () => {
  return (
    <Container maxW="md" py={12}>
      <VStack spacing={8}>
        <Box textAlign="center">
          <Heading size="lg" mb={2}>
            Welcome Back
          </Heading>
          <Text color="gray.600">
            Sign in to your Interior Designer account
          </Text>
        </Box>
        
        <Box w="full">
          <ClerkSignIn 
            appearance={{
              elements: {
                formButtonPrimary: {
                  backgroundColor: '#3182CE',
                  '&:hover': {
                    backgroundColor: '#2C5282'
                  }
                },
                card: {
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  borderRadius: '8px'
                }
              }
            }}
            redirectUrl="/"
            signUpUrl="/sign-up"
          />
        </Box>
      </VStack>
    </Container>
  );
};

export default SignIn;
