import React from 'react';
import { useA<PERSON>, use<PERSON>ser, SignOutButton } from '@clerk/clerk-react';
import {
  Box,
  Button,
  HStack,
  Text,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  useColorModeValue
} from '@chakra-ui/react';
import { Link } from '@tanstack/react-router';

interface AuthWrapperProps {
  children: React.ReactNode;
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const { isSignedIn } = useAuth();
  const { user } = useUser();

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  if (!isSignedIn) {
    return <>{children}</>;
  }

  return (
    <Box>
      {/* Auth Header */}
      <Box
        bg={bgColor}
        borderBottom="1px"
        borderColor={borderColor}
        px={6}
        py={4}
      >
        <HStack justify="space-between">
          <Text fontSize="lg" fontWeight="bold">
            Interior Designer
          </Text>

          <HStack spacing={4}>
            <Text fontSize="sm" color="gray.600">
              Welcome, {user?.firstName || user?.emailAddresses[0]?.emailAddress}
            </Text>

            <Menu>
              <MenuButton>
                <Avatar
                  size="sm"
                  name={user?.fullName || undefined}
                  src={user?.imageUrl || undefined}
                />
              </MenuButton>
              <MenuList>
                <MenuItem>
                  <Link to="/profile" style={{ width: '100%' }}>
                    Profile Settings
                  </Link>
                </MenuItem>
                <MenuDivider />
                <MenuItem>
                  <SignOutButton>
                    <Button variant="ghost" size="sm" w="full" justifyContent="flex-start">
                      Sign Out
                    </Button>
                  </SignOutButton>
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </HStack>
      </Box>

      {/* Main Content */}
      <Box>
        {children}
      </Box>
    </Box>
  );
};

export default AuthWrapper;
