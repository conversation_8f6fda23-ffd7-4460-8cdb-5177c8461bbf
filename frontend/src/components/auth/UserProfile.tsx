import React from 'react';
import { UserProfile as ClerkUserProfile } from '@clerk/clerk-react';
import { Box, Container, Heading, VStack } from '@chakra-ui/react';

const UserProfile: React.FC = () => {
  return (
    <Container maxW="2xl" py={12}>
      <VStack spacing={8}>
        <Box textAlign="center">
          <Heading size="lg" mb={2}>
            Profile Settings
          </Heading>
        </Box>
        
        <Box w="full">
          <ClerkUserProfile 
            appearance={{
              elements: {
                formButtonPrimary: {
                  backgroundColor: '#3182CE',
                  '&:hover': {
                    backgroundColor: '#2C5282'
                  }
                },
                card: {
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  borderRadius: '8px'
                }
              }
            }}
          />
        </Box>
      </VStack>
    </Container>
  );
};

export default UserProfile;
