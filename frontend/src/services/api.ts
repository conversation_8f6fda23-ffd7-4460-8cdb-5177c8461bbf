/**
 * API service for communicating with the backend
 */
import { useAuth } from '@clerk/clerk-react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';

export interface UserProfile {
  user_id: string;
  email: string;
  email_verified: boolean;
  first_name?: string;
  last_name?: string;
  full_name?: string;
}

export interface Design {
  id: string;
  name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  data: any;
}

export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

class ApiService {
  private getAuthHeaders(token?: string): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = this.getAuthHeaders(token);

    const response = await fetch(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  // Public endpoints (no authentication required)
  async getHealth(): Promise<{ status: string; service: string }> {
    return this.request('/api/health');
  }

  async getVersion(): Promise<{ version: string; name: string }> {
    return this.request('/api/version');
  }

  async getPublicDesigns(): Promise<{ message: string; designs: Design[] }> {
    return this.request('/api/public/designs');
  }

  // Protected endpoints (authentication required)
  async getUserProfile(token: string): Promise<UserProfile> {
    return this.request('/api/user/profile', {}, token);
  }

  async getUserDesigns(token: string): Promise<{ user_id: string; designs: Design[]; message?: string }> {
    return this.request('/api/user/designs', {}, token);
  }

  async createDesign(designData: any, token: string): Promise<{ user_id: string; design_id: string; message?: string }> {
    return this.request('/api/user/designs', {
      method: 'POST',
      body: JSON.stringify(designData),
    }, token);
  }
}

export const apiService = new ApiService();

// React hook for API calls with authentication
export const useApiService = () => {
  const { getToken } = useAuth();

  const callWithAuth = async <T>(
    apiCall: (token: string) => Promise<T>
  ): Promise<T> => {
    const token = await getToken();
    if (!token) {
      throw new Error('No authentication token available');
    }
    return apiCall(token);
  };

  return {
    // Public methods
    getHealth: () => apiService.getHealth(),
    getVersion: () => apiService.getVersion(),
    getPublicDesigns: () => apiService.getPublicDesigns(),

    // Authenticated methods
    getUserProfile: () => callWithAuth(apiService.getUserProfile),
    getUserDesigns: () => callWithAuth(apiService.getUserDesigns),
    createDesign: (designData: any) => callWithAuth((token) => apiService.createDesign(designData, token)),
  };
};
